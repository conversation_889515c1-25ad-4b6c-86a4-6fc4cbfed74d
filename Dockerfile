# --------------------------------------------------------------
#  Dockerfile – LangChain / LangGraph experimental VM
# --------------------------------------------------------------

# ---- Base image ------------------------------------------------
# Use the official slim Python image (3‑11 at time of writing)
FROM python:3.11-slim

# ---- OS level dependencies --------------------------------------
# - git   : needed if you pull repos inside the container
# - gcc & libffi-dev : required to compile some Python wheels
# - curl  : handy for health checks / debugging
# - build-essential (gcc, g++) for any C extensions
# - tini  : proper init process (reaps zombies)
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        git \
        curl \
        gcc \
        g++ \
        libffi-dev \
        make \
        build-essential \
        tini && \
    rm -rf /var/lib/apt/lists/*

# ---- Set up a non‑root user ------------------------------------
ARG USERNAME=appuser
ARG UID=1000
ARG GID=1000

RUN groupadd --gid $GID $USERNAME && \
    useradd --uid $UID --gid $GID -m $USERNAME && \
    mkdir -p /home/<USER>/.cache && \
    chown -R $USERNAME:$USERNAME /home/<USER>

# ---- Working directory -----------------------------------------
WORKDIR /app

# ---- Python environment ----------------------------------------
# Create a virtualenv inside the image (helps keep stdlib clean)
ENV VIRTUAL_ENV=/opt/venv
RUN python -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Upgrade pip and install core tooling
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# ---- Install LangChain / LangGraph ------------------------------
# If you have a requirements.txt in the host repo, copy it just for caching.
# (Docker will reuse layers when requirements don’t change.)
COPY ./requirements.txt /tmp/requirements.txt
RUN if [ -f /tmp/requirements.txt ]; then \
        pip install --no-cache-dir -r /tmp/requirements.txt; \
    else \
        # Fallback minimal set – you can add more as needed
        pip install --no-cache-dir \
            "langchain>=0.2,<0.3" \
            "langgraph>=0.1,<0.2" \
            "openai>=1.30,<2"; \
    fi && rm -f /tmp/requirements.txt

# ---- Expose any ports you need ---------------------------------
# Example: 8000 for a FastAPI or Flask API that serves the graph.
EXPOSE 8000

# ---- Runtime user ------------------------------------------------
USER $USERNAME

# ---- Entrypoint & default command -------------------------------
# tini handles proper signal forwarding (Ctrl‑C, SIGTERM…)
ENTRYPOINT ["/usr/bin/tini", "--"]
# By default we just launch a bash so you can run whatever you need.
CMD ["bash"]
