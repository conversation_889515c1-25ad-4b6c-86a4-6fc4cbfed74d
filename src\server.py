"""
FastAPI server for LangGraph workflows with web interface.
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import json
import os
from .main import get_local_llm, check_local_models, create_llm_graph, create_simple_graph

app = FastAPI(
    title="LangGraph Workflow Interface",
    description="Web interface for creating and running LangGraph workflows",
    version="1.0.0"
)

# Pydantic models for API
class WorkflowInput(BaseModel):
    workflow_type: str
    input_data: Dict[str, Any]

class WorkflowResponse(BaseModel):
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class ModelStatus(BaseModel):
    available: bool
    model_type: Optional[str] = None
    model_url: Optional[str] = None
    models: List[str] = []

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>LangGraph Workflow Interface</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
            .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .button:hover { background: #0056b3; }
            .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
            textarea { width: 100%; height: 100px; margin: 10px 0; }
            .workflow-result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .model-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔗 LangGraph Workflow Interface</h1>
                <p>Visual interface for creating and running LangGraph workflows with local models</p>
            </div>

            <div class="section">
                <h2>📊 Model Status</h2>
                <button class="button" onclick="checkModels()">Check Local Models</button>
                <div id="model-status"></div>
            </div>

            <div class="section">
                <h2>🚀 Quick Workflows</h2>
                <button class="button" onclick="runSimpleWorkflow()">Run Simple Workflow</button>
                <button class="button" onclick="runLLMWorkflow()">Run LLM Workflow</button>
                <div id="workflow-results"></div>
            </div>

            <div class="section">
                <h2>💬 Custom LLM Chat</h2>
                <textarea id="chat-input" placeholder="Enter your message here...">What is LangGraph and how does it work?</textarea>
                <br>
                <button class="button" onclick="runCustomChat()">Send to LLM</button>
                <div id="chat-results"></div>
            </div>

            <div class="section">
                <h2>🔧 Workflow Builder</h2>
                <p>Build custom workflows by defining nodes and edges:</p>
                <textarea id="workflow-definition" placeholder="Enter workflow definition (JSON)...">{
  "nodes": ["input", "process", "output"],
  "edges": [["input", "process"], ["process", "output"]],
  "input": "Hello from custom workflow!"
}</textarea>
                <br>
                <button class="button" onclick="runCustomWorkflow()">Run Custom Workflow</button>
                <div id="custom-results"></div>
            </div>
        </div>

        <script>
            async function checkModels() {
                try {
                    const response = await fetch('/api/models/status');
                    const data = await response.json();
                    const statusDiv = document.getElementById('model-status');
                    
                    if (data.available) {
                        statusDiv.innerHTML = `
                            <div class="status success model-info">
                                <strong>✅ ${data.model_type.toUpperCase()} Available</strong><br>
                                URL: ${data.model_url}<br>
                                Models: ${data.models.join(', ')}
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="status warning">
                                ⚠️ No local models detected. Please start Ollama or LM Studio.
                            </div>
                        `;
                    }
                } catch (error) {
                    document.getElementById('model-status').innerHTML = `
                        <div class="status error">❌ Error checking models: ${error.message}</div>
                    `;
                }
            }

            async function runSimpleWorkflow() {
                try {
                    const response = await fetch('/api/workflow/simple', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            workflow_type: 'simple',
                            input_data: {message: 'Hello from web interface!', step: 0}
                        })
                    });
                    const data = await response.json();
                    displayWorkflowResult('workflow-results', data);
                } catch (error) {
                    displayError('workflow-results', error.message);
                }
            }

            async function runLLMWorkflow() {
                try {
                    const response = await fetch('/api/workflow/llm', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            workflow_type: 'llm',
                            input_data: {
                                user_input: 'Explain LangGraph in simple terms',
                                llm_response: '',
                                processed: false
                            }
                        })
                    });
                    const data = await response.json();
                    displayWorkflowResult('workflow-results', data);
                } catch (error) {
                    displayError('workflow-results', error.message);
                }
            }

            async function runCustomChat() {
                const input = document.getElementById('chat-input').value;
                if (!input.trim()) return;

                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({message: input})
                    });
                    const data = await response.json();
                    displayWorkflowResult('chat-results', data);
                } catch (error) {
                    displayError('chat-results', error.message);
                }
            }

            async function runCustomWorkflow() {
                const definition = document.getElementById('workflow-definition').value;
                try {
                    const workflowData = JSON.parse(definition);
                    const response = await fetch('/api/workflow/custom', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            workflow_type: 'custom',
                            input_data: workflowData
                        })
                    });
                    const data = await response.json();
                    displayWorkflowResult('custom-results', data);
                } catch (error) {
                    displayError('custom-results', error.message);
                }
            }

            function displayWorkflowResult(elementId, data) {
                const element = document.getElementById(elementId);
                if (data.success) {
                    element.innerHTML = `
                        <div class="status success workflow-result">
                            <strong>✅ Success!</strong><br>
                            <pre>${JSON.stringify(data.result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    element.innerHTML = `
                        <div class="status error">❌ Error: ${data.error}</div>
                    `;
                }
            }

            function displayError(elementId, message) {
                document.getElementById(elementId).innerHTML = `
                    <div class="status error">❌ Error: ${message}</div>
                `;
            }

            // Check models on page load
            window.onload = function() {
                checkModels();
            };
        </script>
    </body>
    </html>
    """

@app.get("/api/models/status", response_model=ModelStatus)
async def get_model_status():
    """Get the status of local models"""
    try:
        model_type, model_url = check_local_models()
        if model_type and model_url:
            # Get available models
            import requests
            models = []
            if model_type == "ollama":
                response = requests.get(f"{model_url}/api/tags", timeout=5)
                if response.status_code == 200:
                    models = [m["name"] for m in response.json().get("models", [])]
            elif model_type == "lmstudio":
                response = requests.get(f"{model_url}/v1/models", timeout=5)
                if response.status_code == 200:
                    models = [m["id"] for m in response.json().get("data", [])]
            
            return ModelStatus(
                available=True,
                model_type=model_type,
                model_url=model_url,
                models=models
            )
        else:
            return ModelStatus(available=False)
    except Exception as e:
        return ModelStatus(available=False)

@app.post("/api/workflow/simple", response_model=WorkflowResponse)
async def run_simple_workflow(input_data: WorkflowInput):
    """Run a simple LangGraph workflow"""
    try:
        graph = create_simple_graph()
        result = graph.invoke(input_data.input_data)
        return WorkflowResponse(success=True, result=result)
    except Exception as e:
        return WorkflowResponse(success=False, error=str(e))

@app.post("/api/workflow/llm", response_model=WorkflowResponse)
async def run_llm_workflow(input_data: WorkflowInput):
    """Run an LLM-powered LangGraph workflow"""
    try:
        graph = create_llm_graph()
        result = graph.invoke(input_data.input_data)
        return WorkflowResponse(success=True, result=result)
    except Exception as e:
        return WorkflowResponse(success=False, error=str(e))

@app.post("/api/chat", response_model=WorkflowResponse)
async def chat_with_llm(message_data: dict):
    """Direct chat with local LLM"""
    try:
        llm = get_local_llm()
        if not llm:
            return WorkflowResponse(success=False, error="No local LLM available")
        
        response = llm.invoke(message_data["message"])
        return WorkflowResponse(success=True, result={"response": str(response)})
    except Exception as e:
        return WorkflowResponse(success=False, error=str(e))

@app.post("/api/workflow/custom", response_model=WorkflowResponse)
async def run_custom_workflow(input_data: WorkflowInput):
    """Run a custom workflow (placeholder for future implementation)"""
    try:
        # This is a placeholder - you can implement custom workflow logic here
        workflow_data = input_data.input_data
        result = {
            "message": "Custom workflow executed",
            "input_received": workflow_data,
            "status": "completed"
        }
        return WorkflowResponse(success=True, result=result)
    except Exception as e:
        return WorkflowResponse(success=False, error=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
