"""
LangChain/LangGraph experimental package.

This package provides a minimal setup for experimenting with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>
in a containerized environment.
"""

from .main import hello, create_simple_graph, create_llm_graph, get_local_llm, check_local_models

__version__ = "0.1.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

# Public API
__all__ = [
    "hello",
    "create_simple_graph",
    "create_llm_graph",
    "get_local_llm",
    "check_local_models",
]

# Web interfaces available
WEB_INTERFACES = {
    "fastapi": "src.server:app",
    "streamlit": "src.streamlit_app",
    "langgraph_studio": "langgraph dev"
}

# Package metadata
__title__ = "LangGraph Experimental"
__description__ = "A minimal LangChain/LangGraph experimental environment"