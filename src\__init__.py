"""
LangChain/LangGraph experimental package.

This package provides a minimal setup for experimenting with <PERSON><PERSON><PERSON><PERSON> and Lang<PERSON>rap<PERSON>
in a containerized environment.
"""

from .main import hello, create_simple_graph

__version__ = "0.1.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

# Public API
__all__ = [
    "hello",
    "create_simple_graph",
]

# Package metadata
__title__ = "LangGraph Experimental"
__description__ = "A minimal LangChain/LangGraph experimental environment"