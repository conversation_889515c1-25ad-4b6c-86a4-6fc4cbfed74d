# --------------------------------------------------------------
# docker-compose.yml – LangGraph dev environment
# --------------------------------------------------------------

version: "3.9"

services:
  langgraph:
    build:
      context: .
      dockerfile: Dockerfile
    image: langgraph-dev:latest
    container_name: langgraph_dev
    # Mount the whole repo (except what's ignored) into /app inside the container.
    volumes:
      - ./:/app:cached   # Windows path `.` resolves to C:\path\to\my-langgraph-project
    # Optionally mount a separate config directory if you keep it outside the repo:
    # - ./config:/config:ro
    environment:
      # Example env vars – adapt to your own keys / settings
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      LANGCHAIN_TRACING_V2: "true"
      LANGCHAIN_ENDPOINT: "https://api.smith.langchain.com"
    ports:
      - "8000:8000"   # expose the container's 8000 on host 8000
    command: ["bash", "-c", "python -m src.main"]   # replace with your run command
    # If you are running a FastAPI server, e.g. `uvicorn src.main:app --host 0.0.0.0 --port 8000`
    # command: ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]