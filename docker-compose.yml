# --------------------------------------------------------------
# docker-compose.yml – LangGraph dev environment
# --------------------------------------------------------------

version: "3.9"

services:
  langgraph:
    build:
      context: .
      dockerfile: Dockerfile
    image: langgraph-dev:latest
    container_name: langgraph_dev
    # Mount the whole repo (except what's ignored) into /app inside the container.
    volumes:
      - ./:/app:cached   # Windows path `.` resolves to C:\path\to\my-langgraph-project
    # Optionally mount a separate config directory if you keep it outside the repo:
    # - ./config:/config:ro
    environment:
      # Local model server configuration
      # Ollama (default port 11434)
      OLLAMA_BASE_URL: "http://host.docker.internal:11434"
      # LM Studio (default port 1234)
      LMSTUDIO_BASE_URL: "http://host.docker.internal:1234"

      # Optional: OpenAI API for fallback
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}

      # LangChain tracing (optional)
      LANGCHAIN_TRACING_V2: ${LANGCHAIN_TRACING_V2:-false}
      LANGCHAIN_ENDPOINT: "https://api.smith.langchain.com"
      LANGCHAIN_API_KEY: ${LANGCHAIN_API_KEY:-}
    ports:
      - "8000:8000"   # FastAPI server for LangGraph workflows
      - "8001:8001"   # LangGraph Studio interface
    # Default command - can be overridden
    command: ["bash", "-c", "cd /app && python src/main.py"]
    # Alternative commands:
    # For FastAPI server: ["uvicorn", "src.server:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    # For LangGraph Studio: ["bash", "-c", "cd /app && langgraph dev --host 0.0.0.0 --port 8001"]
    # For interactive shell: ["bash"]