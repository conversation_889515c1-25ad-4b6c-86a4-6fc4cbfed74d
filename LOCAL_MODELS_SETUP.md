# Local Models Setup Guide

This guide explains how to set up local LLM servers (Ollama or LM Studio) on your Windows host to work with the Docker container.

## Option 1: Using Ollama

### Installation
1. Download Ollama from [https://ollama.ai](https://ollama.ai)
2. Install and run Ollama on Windows
3. Pull a model: `ollama pull llama2` (or `llama3`, `mistral`, etc.)

### Verification
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Test a model
ollama run llama2 "Hello, how are you?"
```

### Popular Models to Try
- `ollama pull llama2` - Meta's Llama 2 (7B)
- `ollama pull llama3` - Meta's Llama 3 (8B)
- `ollama pull mistral` - Mistral 7B
- `ollama pull codellama` - Code-focused model
- `ollama pull phi3` - Microsoft's Phi-3 (3.8B, fast)

## Option 2: Using LM Studio

### Installation
1. Download LM Studio from [https://lmstudio.ai](https://lmstudio.ai)
2. Install and launch LM Studio
3. Download a model from the built-in model browser
4. Load the model and start the local server

### Configuration in LM Studio
1. Go to "Local Server" tab
2. Select your loaded model
3. Click "Start Server" (default port 1234)
4. Ensure "Cross-Origin-Resource-Sharing (CORS)" is enabled

### Verification
```bash
# Check if LM Studio is running
curl http://localhost:1234/v1/models
```

## Docker Configuration

The docker-compose.yml is already configured to connect to both services:

- **Ollama**: `http://host.docker.internal:11434`
- **LM Studio**: `http://host.docker.internal:1234`

The `host.docker.internal` hostname allows the Docker container to access services running on the Windows host.

## Environment Variables

You can customize the URLs by setting environment variables:

```bash
# For Ollama
OLLAMA_BASE_URL=http://host.docker.internal:11434

# For LM Studio  
LMSTUDIO_BASE_URL=http://host.docker.internal:1234
```

## Testing the Setup

1. Start your local model server (Ollama or LM Studio)
2. Rebuild and run the Docker container:
   ```bash
   docker compose down
   docker compose up --build
   ```
3. The application will automatically detect and use available local models

## Troubleshooting

### Container can't connect to host services
- Ensure Docker Desktop is running
- Check that your firewall allows connections on ports 11434 (Ollama) or 1234 (LM Studio)
- Try using `localhost` instead of `host.docker.internal` if needed

### Model loading issues
- **Ollama**: Make sure you've pulled at least one model with `ollama pull <model-name>`
- **LM Studio**: Ensure a model is loaded and the server is started

### Performance tips
- Use smaller models (7B or less) for faster responses
- Adjust temperature settings in the code for different response styles
- Consider using quantized models for better performance

## Model Recommendations by Use Case

| Use Case | Ollama | LM Studio |
|----------|--------|-----------|
| **General Chat** | `llama3`, `mistral` | Llama 3 8B, Mistral 7B |
| **Code Generation** | `codellama`, `deepseek-coder` | CodeLlama, DeepSeek Coder |
| **Fast Responses** | `phi3`, `gemma` | Phi-3, Gemma 2B |
| **Reasoning** | `llama3:70b` (if you have RAM) | Llama 3 70B |

## Next Steps

Once you have local models running, you can:
- Modify the prompts in `src/main.py`
- Create custom LangGraph workflows
- Experiment with different model parameters
- Build more complex AI applications
