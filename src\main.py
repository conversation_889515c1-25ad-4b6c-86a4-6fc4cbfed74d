from langchain import PromptTemplate, <PERSON><PERSON>hain
from langgraph.graph import Graph

def hello():
    print("✅ LangGraph container is up and running!")
    # Minimal demo – you can replace with your own graph logic.
    prompt = PromptTemplate.from_template("Say hello to {name}!")
    chain = LLMChain(prompt=prompt, llm=None)   # placeholder; normally you'd pass OpenAI()
    print(prompt.format(name="world"))

if __name__ == "__main__":
    hello()