from langchain_core.prompts import PromptTemplate
from langgraph.graph import StateGraph
import os

def hello():
    print("✅ LangGraph container is up and running!")

    # Modern LangChain approach - using prompt templates directly
    prompt = PromptTemplate.from_template("Say hello to {name}!")
    formatted_prompt = prompt.format(name="world")
    print(f"Generated prompt: {formatted_prompt}")

    # Simple demonstration of LangGraph StateGraph
    print("🔗 LangGraph is available and ready for graph-based workflows!")

    # Check if environment variables are set up
    if os.getenv("OPENAI_API_KEY"):
        print("🔑 OpenAI API key is configured")
    else:
        print("⚠️  OpenAI API key not found - set OPENAI_API_KEY environment variable to use LLMs")

def create_simple_graph():
    """Example of creating a simple LangGraph StateGraph"""
    from typing import TypedDict

    class GraphState(TypedDict):
        message: str
        step: int

    def step_1(state: GraphState) -> GraphState:
        return {"message": f"Step 1: {state['message']}", "step": 1}

    def step_2(state: GraphState) -> GraphState:
        return {"message": f"Step 2: {state['message']}", "step": 2}

    # Create the graph
    workflow = StateGraph(GraphState)
    workflow.add_node("step1", step_1)
    workflow.add_node("step2", step_2)
    workflow.add_edge("step1", "step2")
    workflow.set_entry_point("step1")
    workflow.set_finish_point("step2")

    return workflow.compile()

if __name__ == "__main__":
    hello()

    # Demonstrate the graph
    print("\n🔄 Testing LangGraph workflow:")
    graph = create_simple_graph()
    result = graph.invoke({"message": "Hello LangGraph!", "step": 0})
    print(f"Graph result: {result}")