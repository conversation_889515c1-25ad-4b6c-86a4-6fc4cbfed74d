from langchain_core.prompts import PromptTemplate
from langgraph.graph import StateGraph
import os
import requests

def check_local_models():
    """Check which local model servers are available"""
    print("🔍 Checking local model servers...")

    # Check Ollama
    ollama_url = os.getenv("OLLAMA_BASE_URL", "http://host.docker.internal:11434")
    try:
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama is running at {ollama_url}")
            if models:
                print(f"   Available models: {[m['name'] for m in models[:3]]}")
            else:
                print("   No models found - run 'ollama pull llama2' or similar")
            return "ollama", ollama_url
    except Exception as e:
        print(f"❌ Ollama not available at {ollama_url}: {e}")

    # Check LM Studio
    lmstudio_url = os.getenv("LMSTUDIO_BASE_URL", "http://host.docker.internal:1234")
    try:
        response = requests.get(f"{lmstudio_url}/v1/models", timeout=5)
        if response.status_code == 200:
            models = response.json().get("data", [])
            print(f"✅ LM Studio is running at {lmstudio_url}")
            if models:
                print(f"   Available models: {[m['id'] for m in models[:3]]}")
            else:
                print("   No models loaded - load a model in LM Studio")
            return "lmstudio", lmstudio_url
    except Exception as e:
        print(f"❌ LM Studio not available at {lmstudio_url}: {e}")

    return None, None

def hello():
    print("✅ LangGraph container is up and running!")

    # Check local model availability
    model_type, model_url = check_local_models()

    # Modern LangChain approach - using prompt templates directly
    prompt = PromptTemplate.from_template("Say hello to {name}!")
    formatted_prompt = prompt.format(name="world")
    print(f"Generated prompt: {formatted_prompt}")

    # Simple demonstration of LangGraph StateGraph
    print("🔗 LangGraph is available and ready for graph-based workflows!")

    # Check if environment variables are set up
    if os.getenv("OPENAI_API_KEY"):
        print("🔑 OpenAI API key is configured")
    else:
        print("⚠️  OpenAI API key not found - using local models instead")

def get_local_llm():
    """Get an LLM instance from available local servers"""
    model_type, model_url = check_local_models()

    if model_type == "ollama":
        try:
            from langchain_ollama import OllamaLLM
            # Try to get the first available model
            response = requests.get(f"{model_url}/api/tags")
            models = response.json().get("models", [])
            if models:
                model_name = models[0]["name"]
                print(f"🦙 Using Ollama model: {model_name}")
                return OllamaLLM(
                    base_url=model_url,
                    model=model_name,
                    temperature=0.7
                )
        except Exception as e:
            print(f"Failed to initialize Ollama: {e}")

    elif model_type == "lmstudio":
        try:
            from langchain_openai import ChatOpenAI
            # LM Studio uses OpenAI-compatible API
            response = requests.get(f"{model_url}/v1/models")
            models = response.json().get("data", [])
            if models:
                model_name = models[0]["id"]
                print(f"🏠 Using LM Studio model: {model_name}")
                return ChatOpenAI(
                    base_url=f"{model_url}/v1",
                    api_key="lm-studio",  # LM Studio doesn't require real API key
                    model=model_name,
                    temperature=0.7
                )
        except Exception as e:
            print(f"Failed to initialize LM Studio: {e}")

    print("⚠️  No local models available")
    return None

def test_local_llm():
    """Test the local LLM with a simple prompt"""
    llm = get_local_llm()
    if llm:
        try:
            print("\n🧠 Testing local LLM...")
            prompt = "Hello! Please respond with a brief greeting."
            response = llm.invoke(prompt)
            print(f"LLM Response: {response}")
            return True
        except Exception as e:
            print(f"Error testing LLM: {e}")
    return False

def create_simple_graph():
    """Example of creating a simple LangGraph StateGraph"""
    from typing import TypedDict

    class GraphState(TypedDict):
        message: str
        step: int

    def step_1(state: GraphState) -> GraphState:
        return {"message": f"Step 1: {state['message']}", "step": 1}

    def step_2(state: GraphState) -> GraphState:
        return {"message": f"Step 2: {state['message']}", "step": 2}

    # Create the graph
    workflow = StateGraph(GraphState)
    workflow.add_node("step1", step_1)
    workflow.add_node("step2", step_2)
    workflow.add_edge("step1", "step2")
    workflow.set_entry_point("step1")
    workflow.set_finish_point("step2")

    return workflow.compile()

def create_llm_graph():
    """Create a LangGraph that uses local LLM"""
    from typing import TypedDict

    class LLMGraphState(TypedDict):
        user_input: str
        llm_response: str
        processed: bool

    def llm_node(state: LLMGraphState) -> LLMGraphState:
        llm = get_local_llm()
        if llm:
            try:
                response = llm.invoke(f"Please respond to: {state['user_input']}")
                return {
                    "user_input": state["user_input"],
                    "llm_response": str(response),
                    "processed": True
                }
            except Exception as e:
                return {
                    "user_input": state["user_input"],
                    "llm_response": f"Error: {e}",
                    "processed": False
                }
        else:
            return {
                "user_input": state["user_input"],
                "llm_response": "No local LLM available",
                "processed": False
            }

    # Create the graph
    workflow = StateGraph(LLMGraphState)
    workflow.add_node("llm", llm_node)
    workflow.set_entry_point("llm")
    workflow.set_finish_point("llm")

    return workflow.compile()

if __name__ == "__main__":
    hello()

    # Test local LLM
    if test_local_llm():
        print("\n🔄 Testing LangGraph with local LLM:")
        llm_graph = create_llm_graph()
        result = llm_graph.invoke({
            "user_input": "What is LangGraph?",
            "llm_response": "",
            "processed": False
        })
        print(f"LLM Graph result: {result}")

    # Demonstrate the simple graph
    print("\n🔄 Testing simple LangGraph workflow:")
    graph = create_simple_graph()
    result = graph.invoke({"message": "Hello LangGraph!", "step": 0})
    print(f"Simple graph result: {result}")