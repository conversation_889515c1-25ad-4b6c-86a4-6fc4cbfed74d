# Core LangChain dependencies
langchain>=0.2,<0.3
langgraph>=0.1,<0.2
langchain-core>=0.2,<0.3

# Local model providers
langchain-ollama>=0.1,<1
langchain-openai>=0.1,<1  # For LM Studio OpenAI-compatible API

# Cloud LLM providers (optional)
openai>=1.30,<2
# anthropic>=0.25,<1
# google-generativeai>=0.5,<1

# Web interface and visualization
fastapi>=0.104,<1
uvicorn[standard]>=0.24,<1
langgraph-cli[inmem]>=0.1,<1
streamlit>=1.28,<2

# Additional useful packages
python-dotenv>=1.0,<2
pydantic>=2.0,<3
requests>=2.31,<3
jinja2>=3.1,<4