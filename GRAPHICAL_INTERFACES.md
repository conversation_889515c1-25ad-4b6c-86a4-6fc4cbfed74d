# LangGraph Graphical Interfaces Guide

This guide explains how to use the various graphical interfaces available for building and managing LangGraph workflows.

## 🎯 Available Interfaces

### 1. FastAPI Web Interface (Recommended for Development)
**Port:** 8000  
**Features:** REST API + Interactive Web UI

### 2. Streamlit Interface (Best for Experimentation)
**Port:** 8001  
**Features:** Interactive dashboard with real-time feedback

### 3. LangGraph Studio (Official Visual Editor)
**Port:** 8001  
**Features:** Official visual workflow builder

## 🚀 Quick Start

### Option 1: FastAPI Interface

```bash
# Start the FastAPI server
docker compose run --rm -p 8000:8000 langgraph uvicorn src.server:app --host 0.0.0.0 --port 8000 --reload

# Or modify docker-compose.yml command to:
# command: ["uvicorn", "src.server:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

**Access:** http://localhost:8000

**Features:**
- ✅ Model status checking
- 🚀 Quick workflow execution
- 💬 Direct LLM chat
- 🔧 Custom workflow builder
- 📊 JSON API endpoints

### Option 2: Streamlit Interface

```bash
# Start Streamlit
docker compose run --rm -p 8001:8001 langgraph streamlit run src/streamlit_app.py --server.port 8001 --server.address 0.0.0.0

# Or use the alternative command:
# command: ["streamlit", "run", "src/streamlit_app.py", "--server.port", "8001", "--server.address", "0.0.0.0"]
```

**Access:** http://localhost:8001

**Features:**
- 🎛️ Interactive sidebar controls
- 💬 Real-time chat interface
- 📊 Workflow visualization
- 🔧 Live configuration
- 📈 Session state management

### Option 3: LangGraph Studio

```bash
# Start LangGraph Studio
docker compose run --rm -p 8001:8001 langgraph bash -c "cd /app && langgraph dev --host 0.0.0.0 --port 8001"
```

**Access:** http://localhost:8001

**Features:**
- 🎨 Visual workflow designer
- 🔗 Node-based graph building
- 🔍 Workflow debugging
- 📊 Execution tracing
- 💾 Workflow persistence

## 🔧 Interface Comparison

| Feature | FastAPI | Streamlit | LangGraph Studio |
|---------|---------|-----------|------------------|
| **Ease of Use** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Customization** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Visual Design** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **API Access** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| **Real-time** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Development** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 📱 Using the FastAPI Interface

### Main Features:

1. **Model Status Dashboard**
   - Check if Ollama/LM Studio is running
   - View available models
   - Connection diagnostics

2. **Quick Workflows**
   - Run predefined workflows with one click
   - View results in formatted JSON
   - Error handling and feedback

3. **Custom Chat**
   - Direct conversation with local LLM
   - Customizable prompts
   - Response streaming

4. **Workflow Builder**
   - Define custom workflows via JSON
   - Test workflow definitions
   - Export/import workflows

### API Endpoints:

```bash
# Check model status
GET /api/models/status

# Run simple workflow
POST /api/workflow/simple

# Run LLM workflow
POST /api/workflow/llm

# Chat with LLM
POST /api/chat

# Custom workflow
POST /api/workflow/custom
```

## 🎛️ Using the Streamlit Interface

### Main Sections:

1. **Sidebar Configuration**
   - Model status checking
   - Workflow type selection
   - Real-time updates

2. **Workflow Execution**
   - Interactive forms for each workflow type
   - Live result display
   - Error handling

3. **Direct LLM Chat**
   - Persistent chat history
   - Session state management
   - Real-time responses

4. **Visualization**
   - Workflow structure diagrams
   - Setup instructions
   - Development notes

### Tips for Streamlit:
- Use the sidebar to configure settings
- Chat history persists during the session
- Refresh the page to reset state
- Use the expander sections for detailed info

## 🎨 Using LangGraph Studio

### Getting Started:

1. **Create a workflow file** (already done in `src/main.py`)
2. **Configure langgraph.json** (already created)
3. **Start the studio** with the command above
4. **Open the web interface** at http://localhost:8001

### Studio Features:

1. **Visual Graph Builder**
   - Drag and drop nodes
   - Connect nodes with edges
   - Configure node properties

2. **Workflow Testing**
   - Run workflows step by step
   - Debug execution flow
   - View intermediate results

3. **Code Generation**
   - Export workflows to Python code
   - Import existing workflows
   - Version control integration

## 🔄 Switching Between Interfaces

You can run multiple interfaces simultaneously on different ports:

```bash
# Terminal 1: FastAPI (port 8000)
docker compose run --rm -p 8000:8000 langgraph uvicorn src.server:app --host 0.0.0.0 --port 8000

# Terminal 2: Streamlit (port 8001)
docker compose run --rm -p 8001:8001 langgraph streamlit run src/streamlit_app.py --server.port 8001 --server.address 0.0.0.0

# Terminal 3: LangGraph Studio (port 8002)
docker compose run --rm -p 8002:8002 langgraph bash -c "cd /app && langgraph dev --host 0.0.0.0 --port 8002"
```

## 🛠️ Customizing the Interfaces

### Adding New Workflows:

1. **Define the workflow** in `src/main.py`
2. **Add API endpoint** in `src/server.py`
3. **Update Streamlit interface** in `src/streamlit_app.py`
4. **Register in LangGraph Studio** via `langgraph.json`

### Example: Adding a new workflow

```python
# In src/main.py
def create_my_custom_graph():
    # Your workflow logic here
    pass

# In src/server.py
@app.post("/api/workflow/my-custom")
async def run_my_custom_workflow(input_data: WorkflowInput):
    # API endpoint logic
    pass

# In src/streamlit_app.py
# Add new option to workflow_type selectbox
# Add corresponding UI elements
```

## 🔍 Troubleshooting

### Common Issues:

1. **Interface won't start**
   - Check if ports are available
   - Ensure dependencies are installed
   - Verify Docker container is running

2. **Can't connect to local models**
   - Verify Ollama/LM Studio is running
   - Check firewall settings
   - Test with `curl` commands

3. **Workflows fail**
   - Check model availability
   - Verify input format
   - Check container logs

### Debug Commands:

```bash
# Check container logs
docker compose logs langgraph

# Test model connectivity
docker compose exec langgraph curl http://host.docker.internal:11434/api/tags

# Interactive debugging
docker compose run --rm langgraph bash
```

## 🎯 Best Practices

1. **Start with Streamlit** for experimentation
2. **Use FastAPI** for API development
3. **Use LangGraph Studio** for complex visual workflows
4. **Test locally** before deploying
5. **Monitor resource usage** with local models
6. **Save workflow definitions** for reuse

## 🚀 Next Steps

1. **Experiment** with different interfaces
2. **Create custom workflows** for your use cases
3. **Integrate** with external APIs
4. **Deploy** to production environments
5. **Share** workflows with your team
