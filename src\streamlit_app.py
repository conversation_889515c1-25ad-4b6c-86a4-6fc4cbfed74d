"""
Streamlit interface for LangGraph workflows.
"""

import streamlit as st
import json
from typing import Dict, Any
from .main import get_local_llm, check_local_models, create_llm_graph, create_simple_graph

# Page configuration
st.set_page_config(
    page_title="LangGraph Workflow Studio",
    page_icon="🔗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .status-box {
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .success { background-color: #d4edda; border-left: 4px solid #28a745; }
    .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
    .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
</style>
""", unsafe_allow_html=True)

# Main header
st.markdown("""
<div class="main-header">
    <h1>🔗 LangGraph Workflow Studio</h1>
    <p>Visual interface for creating and running LangGraph workflows with local models</p>
</div>
""", unsafe_allow_html=True)

# Sidebar for model status and configuration
with st.sidebar:
    st.header("🔧 Configuration")
    
    # Model status check
    if st.button("🔍 Check Local Models", use_container_width=True):
        with st.spinner("Checking local models..."):
            model_type, model_url = check_local_models()
            
            if model_type and model_url:
                st.success(f"✅ {model_type.upper()} detected at {model_url}")
                
                # Get available models
                try:
                    import requests
                    if model_type == "ollama":
                        response = requests.get(f"{model_url}/api/tags", timeout=5)
                        if response.status_code == 200:
                            models = [m["name"] for m in response.json().get("models", [])]
                            st.info(f"Available models: {', '.join(models)}")
                    elif model_type == "lmstudio":
                        response = requests.get(f"{model_url}/v1/models", timeout=5)
                        if response.status_code == 200:
                            models = [m["id"] for m in response.json().get("data", [])]
                            st.info(f"Available models: {', '.join(models)}")
                except Exception as e:
                    st.warning(f"Could not fetch model list: {e}")
            else:
                st.warning("⚠️ No local models detected. Please start Ollama or LM Studio.")
    
    st.divider()
    
    # Workflow options
    st.header("⚙️ Workflow Options")
    workflow_type = st.selectbox(
        "Select Workflow Type",
        ["Simple Demo", "LLM Chat", "Custom Workflow"],
        help="Choose the type of workflow to run"
    )

# Main content area
col1, col2 = st.columns([1, 1])

with col1:
    st.header("🚀 Workflow Execution")
    
    if workflow_type == "Simple Demo":
        st.subheader("Simple LangGraph Demo")
        st.write("This demonstrates a basic LangGraph workflow without LLM.")
        
        demo_message = st.text_input("Demo Message", value="Hello from Streamlit!")
        
        if st.button("Run Simple Workflow", use_container_width=True):
            with st.spinner("Running workflow..."):
                try:
                    graph = create_simple_graph()
                    result = graph.invoke({"message": demo_message, "step": 0})
                    st.success("✅ Workflow completed!")
                    st.json(result)
                except Exception as e:
                    st.error(f"❌ Error: {e}")
    
    elif workflow_type == "LLM Chat":
        st.subheader("LLM-Powered Workflow")
        st.write("This uses your local LLM in a LangGraph workflow.")
        
        user_input = st.text_area(
            "Your Message",
            value="What is LangGraph and how does it work?",
            height=100
        )
        
        if st.button("Run LLM Workflow", use_container_width=True):
            with st.spinner("Processing with local LLM..."):
                try:
                    graph = create_llm_graph()
                    result = graph.invoke({
                        "user_input": user_input,
                        "llm_response": "",
                        "processed": False
                    })
                    st.success("✅ LLM workflow completed!")
                    
                    # Display results nicely
                    st.write("**User Input:**", result["user_input"])
                    st.write("**LLM Response:**", result["llm_response"])
                    st.write("**Processed:**", "✅" if result["processed"] else "❌")
                    
                except Exception as e:
                    st.error(f"❌ Error: {e}")
    
    elif workflow_type == "Custom Workflow":
        st.subheader("Custom Workflow Builder")
        st.write("Define your own workflow structure.")
        
        # Workflow definition
        workflow_definition = st.text_area(
            "Workflow Definition (JSON)",
            value=json.dumps({
                "nodes": ["input", "process", "output"],
                "edges": [["input", "process"], ["process", "output"]],
                "input_data": "Custom workflow input"
            }, indent=2),
            height=150
        )
        
        if st.button("Run Custom Workflow", use_container_width=True):
            try:
                workflow_data = json.loads(workflow_definition)
                st.success("✅ Custom workflow definition parsed!")
                st.json({
                    "status": "Custom workflow would run here",
                    "definition": workflow_data,
                    "note": "Implement custom logic in the backend"
                })
            except json.JSONDecodeError as e:
                st.error(f"❌ Invalid JSON: {e}")
            except Exception as e:
                st.error(f"❌ Error: {e}")

with col2:
    st.header("💬 Direct LLM Chat")
    st.write("Chat directly with your local model.")
    
    # Chat interface
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []
    
    # Display chat history
    chat_container = st.container()
    with chat_container:
        for i, (user_msg, bot_msg) in enumerate(st.session_state.chat_history):
            st.write(f"**You:** {user_msg}")
            st.write(f"**Bot:** {bot_msg}")
            st.divider()
    
    # Chat input
    with st.form("chat_form", clear_on_submit=True):
        chat_input = st.text_input("Type your message:")
        submitted = st.form_submit_button("Send", use_container_width=True)
        
        if submitted and chat_input:
            with st.spinner("Getting response from local LLM..."):
                try:
                    llm = get_local_llm()
                    if llm:
                        response = llm.invoke(chat_input)
                        st.session_state.chat_history.append((chat_input, str(response)))
                        st.rerun()
                    else:
                        st.error("❌ No local LLM available")
                except Exception as e:
                    st.error(f"❌ Error: {e}")

# Footer with additional information
st.divider()
st.header("📚 Workflow Visualization")

# Workflow visualization section
viz_col1, viz_col2 = st.columns([1, 1])

with viz_col1:
    st.subheader("Simple Workflow Structure")
    st.code("""
    Input → Step 1 → Step 2 → Output
    
    Nodes:
    - step1: Processes initial message
    - step2: Adds additional processing
    
    Flow:
    1. Receive input message
    2. Transform in step 1
    3. Further transform in step 2
    4. Return final result
    """)

with viz_col2:
    st.subheader("LLM Workflow Structure")
    st.code("""
    User Input → LLM Node → Processed Output
    
    Nodes:
    - llm: Processes input with local model
    
    Flow:
    1. Receive user input
    2. Send to local LLM
    3. Process response
    4. Return structured result
    """)

# Instructions
st.header("🔧 Setup Instructions")
st.info("""
**To use this interface:**

1. **Start a local model server:**
   - **Ollama:** `ollama serve` then `ollama pull llama3`
   - **LM Studio:** Download, load a model, and start the server

2. **Run this Streamlit app:**
   ```bash
   streamlit run src/streamlit_app.py --server.port 8001
   ```

3. **Or use the FastAPI interface:**
   ```bash
   uvicorn src.server:app --host 0.0.0.0 --port 8000
   ```

4. **Access the interface:**
   - Streamlit: http://localhost:8001
   - FastAPI: http://localhost:8000
""")

# Development notes
with st.expander("🔧 Development Notes"):
    st.write("""
    **Adding Custom Workflows:**
    1. Define new workflow functions in `src/main.py`
    2. Add API endpoints in `src/server.py`
    3. Update this Streamlit interface
    
    **Extending Functionality:**
    - Add more LLM providers
    - Implement workflow persistence
    - Add workflow templates
    - Create visual workflow builder
    """)
